/**
 * 布局控制工具函数
 * 用于动态控制页面布局元素的显示和隐藏
 */

/**
 * 隐藏左侧菜单和顶部菜单
 */
export function hideMenus() {
    // 隐藏左侧菜单
    const asideElement = document.querySelector('.layout-aside') as HTMLElement;
    if (asideElement) {
        asideElement.style.display = 'none';
    }

    // 隐藏顶部菜单
    const headerElement = document.querySelector('.layout-header') as HTMLElement;
    if (headerElement) {
        headerElement.style.display = 'none';
    }

    // 隐藏面包屑导航
    const breadcrumbElement = document.querySelector('.layout-navbars-breadcrumb') as HTMLElement;
    if (breadcrumbElement) {
        breadcrumbElement.style.display = 'none';
    }

    // 隐藏标签页
    const tagsViewElement = document.querySelector('.layout-navbars-tagsview') as HTMLElement;
    if (tagsViewElement) {
        tagsViewElement.style.display = 'none';
    }

    // 调整主内容区域样式，使其占满全屏
    const mainElement = document.querySelector('.layout-main') as HTMLElement;
    if (mainElement) {
        mainElement.style.width = '100vw';
        mainElement.style.height = '100vh';
        mainElement.style.padding = '0';
        mainElement.style.margin = '0';
    }

    // 调整容器样式
    const containerElement = document.querySelector('.layout-container-view') as HTMLElement;
    if (containerElement) {
        containerElement.style.width = '100vw';
        containerElement.style.height = '100vh';
    }

    // 添加全屏样式类
    document.body.classList.add('fullscreen-mode');
}

/**
 * 显示左侧菜单和顶部菜单
 */
export function showMenus() {
    // 显示左侧菜单
    const asideElement = document.querySelector('.layout-aside') as HTMLElement;
    if (asideElement) {
        asideElement.style.display = '';
    }

    // 显示顶部菜单
    const headerElement = document.querySelector('.layout-header') as HTMLElement;
    if (headerElement) {
        headerElement.style.display = '';
    }

    // 显示面包屑导航
    const breadcrumbElement = document.querySelector('.layout-navbars-breadcrumb') as HTMLElement;
    if (breadcrumbElement) {
        breadcrumbElement.style.display = '';
    }

    // 显示标签页
    const tagsViewElement = document.querySelector('.layout-navbars-tagsview') as HTMLElement;
    if (tagsViewElement) {
        tagsViewElement.style.display = '';
    }

    // 恢复主内容区域样式
    const mainElement = document.querySelector('.layout-main') as HTMLElement;
    if (mainElement) {
        mainElement.style.width = '';
        mainElement.style.height = '';
        mainElement.style.padding = '';
        mainElement.style.margin = '';
    }

    // 恢复容器样式
    const containerElement = document.querySelector('.layout-container-view') as HTMLElement;
    if (containerElement) {
        containerElement.style.width = '';
        containerElement.style.height = '';
    }

    // 移除全屏样式类
    document.body.classList.remove('fullscreen-mode');
}

/**
 * 切换菜单显示状态
 */
export function toggleMenus() {
    const isFullscreen = document.body.classList.contains('fullscreen-mode');
    if (isFullscreen) {
        showMenus();
    } else {
        hideMenus();
    }
}

/**
 * 检查当前是否为全屏模式
 */
export function isFullscreenMode(): boolean {
    return document.body.classList.contains('fullscreen-mode');
}

/**
 * 为页面添加全屏切换按钮
 * @param container 按钮容器选择器
 * @param buttonText 按钮文本
 */
export function addFullscreenToggleButton(container: string = 'body', buttonText: string = '切换全屏') {
    const containerElement = document.querySelector(container);
    if (!containerElement) return;

    // 检查是否已存在按钮
    if (document.querySelector('.fullscreen-toggle-btn')) return;

    const button = document.createElement('button');
    button.className = 'fullscreen-toggle-btn';
    button.textContent = buttonText;
    button.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        padding: 10px 20px;
        background: #409eff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
    `;

    button.addEventListener('mouseenter', () => {
        button.style.background = '#66b1ff';
    });

    button.addEventListener('mouseleave', () => {
        button.style.background = '#409eff';
    });

    button.addEventListener('click', () => {
        toggleMenus();
        button.textContent = isFullscreenMode() ? '退出全屏' : '进入全屏';
    });

    containerElement.appendChild(button);
}

/**
 * 移除全屏切换按钮
 */
export function removeFullscreenToggleButton() {
    const button = document.querySelector('.fullscreen-toggle-btn');
    if (button) {
        button.remove();
    }
}
