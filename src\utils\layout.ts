/**
 * 全局布局控制工具函数
 * 用于动态控制页面布局元素的显示和隐藏
 * 支持所有布局模式：defaults、classic、transverse、columns
 *
 * 功能特性：
 * - 支持所有4种布局模式
 * - 智能状态管理和样式恢复
 * - 键盘快捷键支持
 * - 响应式设计适配
 * - 深色模式兼容
 * - 组合式API支持
 */

import { useThemeConfig } from '/@/stores/themeConfig';
import { storeToRefs } from 'pinia';

// 全局状态管理
let isFullscreenActive = false;
let originalStyles: Map<string, string> = new Map();
let keyboardListener: ((e: KeyboardEvent) => void) | null = null;
let resizeListener: ((e: Event) => void) | null = null;

// 布局元素选择器映射
const LAYOUT_SELECTORS = {
    // 主要布局容器
    container: '.layout-container',
    containerView: '.layout-container-view',
    main: '.layout-main',

    // 菜单相关
    aside: '.layout-aside',
    columnsAside: '.layout-columns-aside',
    columnsWrap: '.layout-columns-warp',

    // 导航相关
    header: '.layout-header',
    breadcrumb: '.layout-navbars-breadcrumb',
    tagsView: '.layout-navbars-tagsview',

    // 其他元素
    logo: '.layout-logo',
    footer: '.layout-footer',

    // 滚动条
    scrollbar: '.layout-backtop',
    mainScrollbar: '.layout-main-scroll'
} as const;

/**
 * 保存元素的原始样式
 */
function saveOriginalStyles(element: HTMLElement, selector: string) {
    if (element && !originalStyles.has(selector)) {
        originalStyles.set(selector, element.style.cssText);
    }
}

/**
 * 恢复元素的原始样式
 */
function restoreOriginalStyles(element: HTMLElement, selector: string) {
    if (element && originalStyles.has(selector)) {
        element.style.cssText = originalStyles.get(selector) || '';
    }
}

/**
 * 获取元素并保存原始样式
 */
function getElementAndSaveStyles(selector: string): HTMLElement | null {
    const element = document.querySelector(selector) as HTMLElement;
    if (element) {
        saveOriginalStyles(element, selector);
    }
    return element;
}

/**
 * 隐藏指定元素
 */
function hideElement(element: HTMLElement): void {
    element.style.display = 'none';
}

/**
 * 设置元素全屏样式
 */
function setFullscreenStyles(element: HTMLElement): void {
    element.style.width = '100vw';
    element.style.height = '100vh';
    element.style.padding = '0';
    element.style.margin = '0';
    element.style.position = 'fixed';
    element.style.top = '0';
    element.style.left = '0';
    element.style.zIndex = '1000';
}

/**
 * 隐藏所有菜单和布局元素
 */
export function hideMenus() {
    if (isFullscreenActive) return;

    try {
        // 获取主题配置
        const themeConfigStore = useThemeConfig();
        const { themeConfig } = storeToRefs(themeConfigStore);
        const currentLayout = themeConfig.value.layout;

        // 隐藏所有布局装饰元素
        const elementsToHide = [
            LAYOUT_SELECTORS.aside,           // 左侧菜单
            LAYOUT_SELECTORS.columnsAside,    // 分栏菜单
            LAYOUT_SELECTORS.header,          // 顶部菜单
            LAYOUT_SELECTORS.breadcrumb,      // 面包屑导航
            LAYOUT_SELECTORS.tagsView,        // 标签页
            LAYOUT_SELECTORS.logo,            // Logo
            LAYOUT_SELECTORS.footer           // 底部版权
        ];

        elementsToHide.forEach(selector => {
            const element = getElementAndSaveStyles(selector);
            if (element) {
                hideElement(element);
            }
        });

        // 调整主内容区域为全屏
        const mainElement = getElementAndSaveStyles(LAYOUT_SELECTORS.main);
        if (mainElement) {
            setFullscreenStyles(mainElement);
        }

        // 调整容器样式
        const containersToAdjust = [
            LAYOUT_SELECTORS.containerView,
            LAYOUT_SELECTORS.container
        ];

        containersToAdjust.forEach(selector => {
            const element = getElementAndSaveStyles(selector);
            if (element) {
                element.style.width = '100vw';
                element.style.height = '100vh';
            }
        });

        // 特殊处理分栏布局
        if (currentLayout === 'columns') {
            const columnsWrapElement = getElementAndSaveStyles(LAYOUT_SELECTORS.columnsWrap);
            if (columnsWrapElement) {
                columnsWrapElement.style.width = '100vw';
                columnsWrapElement.style.height = '100vh';
            }
        }

        // 处理滚动条容器
        const scrollbarElement = getElementAndSaveStyles(LAYOUT_SELECTORS.scrollbar);
        if (scrollbarElement) {
            scrollbarElement.style.width = '100vw';
            scrollbarElement.style.height = '100vh';
        }

        // 添加全屏样式类
        document.body.classList.add('layout-fullscreen-mode');
        document.documentElement.classList.add('layout-fullscreen-mode');

        // 设置状态
        isFullscreenActive = true;

        // 触发自定义事件
        window.dispatchEvent(new CustomEvent('layout-fullscreen-enter', {
            detail: { layout: currentLayout, timestamp: Date.now() }
        }));

        console.log(`✅ 全屏模式已激活 (布局: ${currentLayout})`);

    } catch (error) {
        console.error('❌ 激活全屏模式时发生错误:', error);
        // 发生错误时重置状态
        isFullscreenActive = false;
    }
}

/**
 * 恢复元素原始样式
 */
function restoreElementStyles(selector: string): void {
    const element = document.querySelector(selector) as HTMLElement;
    if (element) {
        restoreOriginalStyles(element, selector);
    }
}

/**
 * 显示所有菜单和布局元素
 */
export function showMenus() {
    if (!isFullscreenActive) return;

    try {
        // 恢复所有布局元素的原始样式
        const elementsToRestore = [
            LAYOUT_SELECTORS.aside,           // 左侧菜单
            LAYOUT_SELECTORS.columnsAside,    // 分栏菜单
            LAYOUT_SELECTORS.header,          // 顶部菜单
            LAYOUT_SELECTORS.breadcrumb,      // 面包屑导航
            LAYOUT_SELECTORS.tagsView,        // 标签页
            LAYOUT_SELECTORS.logo,            // Logo
            LAYOUT_SELECTORS.footer,          // 底部版权
            LAYOUT_SELECTORS.main,            // 主内容区
            LAYOUT_SELECTORS.containerView,   // 容器视图
            LAYOUT_SELECTORS.container,       // 主容器
            LAYOUT_SELECTORS.columnsWrap,     // 分栏包装器
            LAYOUT_SELECTORS.scrollbar        // 滚动条容器
        ];

        elementsToRestore.forEach(selector => {
            restoreElementStyles(selector);
        });

        // 移除全屏样式类
        document.body.classList.remove('layout-fullscreen-mode');
        document.documentElement.classList.remove('layout-fullscreen-mode');

        // 清空原始样式缓存
        originalStyles.clear();

        // 设置状态
        isFullscreenActive = false;

        // 触发自定义事件
        window.dispatchEvent(new CustomEvent('layout-fullscreen-exit', {
            detail: { timestamp: Date.now() }
        }));

        console.log('✅ 已退出全屏模式，布局已恢复');

    } catch (error) {
        console.error('❌ 退出全屏模式时发生错误:', error);
        // 发生错误时也要重置状态
        isFullscreenActive = false;
        originalStyles.clear();
    }
}

/**
 * 切换菜单显示状态
 */
export function toggleMenus() {
    if (isFullscreenActive) {
        showMenus();
    } else {
        hideMenus();
    }
}

/**
 * 检查当前是否为全屏模式
 */
export function isFullscreenMode(): boolean {
    return isFullscreenActive;
}

/**
 * 强制退出全屏模式（用于页面刷新后的状态恢复）
 */
export function forceExitFullscreen() {
    if (document.body.classList.contains('layout-fullscreen-mode')) {
        document.body.classList.remove('layout-fullscreen-mode');
        document.documentElement.classList.remove('layout-fullscreen-mode');
    }
    isFullscreenActive = false;
    originalStyles.clear();
}

/**
 * 启用键盘快捷键支持
 * @param shortcutKey 主要快捷键，默认为 'F11'
 */
export function enableKeyboardShortcut(shortcutKey: string = 'F11') {
    // 移除之前的监听器
    if (keyboardListener) {
        document.removeEventListener('keydown', keyboardListener);
    }

    keyboardListener = (e: KeyboardEvent) => {
        // 检查是否在输入框中，避免干扰正常输入
        const target = e.target as HTMLElement;
        const isInputElement = target.tagName === 'INPUT' ||
                              target.tagName === 'TEXTAREA' ||
                              target.contentEditable === 'true';

        // 主快捷键切换全屏
        if (e.key === shortcutKey && !isInputElement) {
            e.preventDefault();
            toggleMenus();
            return;
        }

        // Ctrl+Shift+F 组合键切换全屏
        if (e.ctrlKey && e.shiftKey && e.key === 'F' && !isInputElement) {
            e.preventDefault();
            toggleMenus();
            return;
        }

        // ESC 键退出全屏（只在全屏模式下生效）
        if (e.key === 'Escape' && isFullscreenActive) {
            e.preventDefault();
            showMenus();
            return;
        }

        // Alt+Enter 切换全屏（备用快捷键）
        if (e.altKey && e.key === 'Enter' && !isInputElement) {
            e.preventDefault();
            toggleMenus();
            return;
        }
    };

    document.addEventListener('keydown', keyboardListener);
    console.log(`⌨️ 键盘快捷键已启用: ${shortcutKey}, Ctrl+Shift+F, Alt+Enter, ESC`);
}

/**
 * 禁用键盘快捷键支持
 */
export function disableKeyboardShortcut() {
    if (keyboardListener) {
        document.removeEventListener('keydown', keyboardListener);
        keyboardListener = null;
    }
}

/**
 * 为页面添加全屏切换按钮
 * @param options 按钮配置选项
 */
export function addFullscreenToggleButton(options: {
    container?: string;
    buttonText?: string;
    position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
    style?: Partial<CSSStyleDeclaration>;
} = {}) {
    const {
        container = 'body',
        buttonText = '切换全屏',
        position = 'top-right',
        style = {}
    } = options;

    const containerElement = document.querySelector(container);
    if (!containerElement) return;

    // 检查是否已存在按钮
    if (document.querySelector('.layout-fullscreen-toggle-btn')) return;

    const button = document.createElement('button');
    button.className = 'layout-fullscreen-toggle-btn';
    button.textContent = isFullscreenActive ? '退出全屏' : buttonText;

    // 设置按钮位置
    const positions = {
        'top-right': { top: '20px', right: '20px' },
        'top-left': { top: '20px', left: '20px' },
        'bottom-right': { bottom: '20px', right: '20px' },
        'bottom-left': { bottom: '20px', left: '20px' }
    };

    const positionStyle = positions[position];

    // 默认样式
    const defaultStyle = {
        position: 'fixed',
        zIndex: '9999',
        padding: '10px 20px',
        background: '#409eff',
        color: 'white',
        border: 'none',
        borderRadius: '6px',
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: '500',
        boxShadow: '0 4px 12px rgba(64, 158, 255, 0.3)',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        userSelect: 'none',
        ...positionStyle,
        ...style
    };

    // 应用样式
    Object.assign(button.style, defaultStyle);

    // 添加事件监听
    button.addEventListener('mouseenter', () => {
        button.style.background = '#66b1ff';
        button.style.transform = 'translateY(-2px)';
        button.style.boxShadow = '0 6px 16px rgba(64, 158, 255, 0.4)';
    });

    button.addEventListener('mouseleave', () => {
        button.style.background = '#409eff';
        button.style.transform = 'translateY(0)';
        button.style.boxShadow = '0 4px 12px rgba(64, 158, 255, 0.3)';
    });

    button.addEventListener('click', () => {
        toggleMenus();
        button.textContent = isFullscreenActive ? '退出全屏' : buttonText;
    });

    // 监听全屏状态变化
    const updateButtonText = () => {
        button.textContent = isFullscreenActive ? '退出全屏' : buttonText;
    };

    window.addEventListener('layout-fullscreen-enter', updateButtonText);
    window.addEventListener('layout-fullscreen-exit', updateButtonText);

    containerElement.appendChild(button);
}

/**
 * 移除全屏切换按钮
 */
export function removeFullscreenToggleButton() {
    const button = document.querySelector('.layout-fullscreen-toggle-btn');
    if (button) {
        button.remove();
    }
}

/**
 * 组合式API：使用全屏布局控制
 * @returns 全屏控制相关的方法和状态
 */
export function useFullscreenLayout() {
    // 动态导入Vue函数，避免在非Vue环境中出错
    let vueRef: any, vueComputed: any, vueOnMounted: any, vueOnUnmounted: any;

    try {
        const vue = require('vue');
        vueRef = vue.ref;
        vueComputed = vue.computed;
        vueOnMounted = vue.onMounted;
        vueOnUnmounted = vue.onUnmounted;
    } catch (error) {
        // 如果Vue不可用，返回基础实现
        console.warn('Vue not available, using basic implementation');
        return {
            isFullscreen: { value: isFullscreenActive },
            enterFullscreen: hideMenus,
            exitFullscreen: showMenus,
            toggleFullscreen: toggleMenus,
            addToggleButton: addFullscreenToggleButton,
            removeToggleButton: removeFullscreenToggleButton,
            enableKeyboard: enableKeyboardShortcut,
            disableKeyboard: disableKeyboardShortcut
        };
    }

    const isFullscreen = vueRef(isFullscreenActive);

    // 更新响应式状态
    const updateFullscreenState = () => {
        isFullscreen.value = isFullscreenActive;
    };

    // 进入全屏
    const enterFullscreen = () => {
        hideMenus();
        updateFullscreenState();
    };

    // 退出全屏
    const exitFullscreen = () => {
        showMenus();
        updateFullscreenState();
    };

    // 切换全屏
    const toggleFullscreen = () => {
        toggleMenus();
        updateFullscreenState();
    };

    // 监听全屏状态变化
    if (vueOnMounted) {
        vueOnMounted(() => {
            window.addEventListener('layout-fullscreen-enter', updateFullscreenState);
            window.addEventListener('layout-fullscreen-exit', updateFullscreenState);

            // 启用键盘快捷键
            enableKeyboardShortcut();

            // 页面刷新时强制退出全屏
            forceExitFullscreen();
        });
    }

    if (vueOnUnmounted) {
        vueOnUnmounted(() => {
            window.removeEventListener('layout-fullscreen-enter', updateFullscreenState);
            window.removeEventListener('layout-fullscreen-exit', updateFullscreenState);

            // 禁用键盘快捷键
            disableKeyboardShortcut();

            // 移除全屏按钮
            removeFullscreenToggleButton();
        });
    }

    return {
        isFullscreen: vueComputed ? vueComputed(() => isFullscreen.value) : isFullscreen,
        enterFullscreen,
        exitFullscreen,
        toggleFullscreen,
        addToggleButton: addFullscreenToggleButton,
        removeToggleButton: removeFullscreenToggleButton,
        enableKeyboard: enableKeyboardShortcut,
        disableKeyboard: disableKeyboardShortcut
    };
}

// 防抖定时器ID
let resizeTimeoutId: ReturnType<typeof setTimeout> | null = null;

/**
 * 处理窗口大小变化
 */
function handleResize() {
    if (!isFullscreenActive) return;

    // 防抖处理，避免频繁调整
    if (resizeTimeoutId) {
        clearTimeout(resizeTimeoutId);
    }

    resizeTimeoutId = setTimeout(() => {
        try {
            // 重新调整主要容器尺寸
            const elementsToResize = [
                LAYOUT_SELECTORS.main,
                LAYOUT_SELECTORS.container,
                LAYOUT_SELECTORS.containerView
            ];

            elementsToResize.forEach(selector => {
                const element = document.querySelector(selector) as HTMLElement;
                if (element) {
                    element.style.width = '100vw';
                    element.style.height = '100vh';
                }
            });

            // 触发自定义事件
            window.dispatchEvent(new CustomEvent('layout-fullscreen-resize', {
                detail: {
                    width: window.innerWidth,
                    height: window.innerHeight,
                    timestamp: Date.now()
                }
            }));

            console.log('🔄 全屏布局已重新调整');
        } catch (error) {
            console.error('❌ 调整全屏布局时发生错误:', error);
        }

        resizeTimeoutId = null;
    }, 150);
}

/**
 * 启用窗口大小变化监听
 */
function enableResizeListener() {
    if (resizeListener) {
        window.removeEventListener('resize', resizeListener);
    }

    resizeListener = handleResize;
    window.addEventListener('resize', resizeListener);
}

/**
 * 禁用窗口大小变化监听
 */
function disableResizeListener() {
    if (resizeListener) {
        window.removeEventListener('resize', resizeListener);
        resizeListener = null;
    }
}

/**
 * 自动初始化全屏布局控制
 * 在应用启动时调用，设置全局快捷键和状态管理
 */
export function initFullscreenLayout() {
    try {
        // 页面加载时强制退出全屏状态
        forceExitFullscreen();

        // 启用全局键盘快捷键
        enableKeyboardShortcut();

        // 启用窗口大小变化监听
        enableResizeListener();

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && isFullscreenActive) {
                console.log('📱 页面隐藏，保持全屏状态');
            } else if (!document.hidden && isFullscreenActive) {
                console.log('📱 页面显示，检查全屏状态');
                // 页面重新显示时，重新调整布局
                handleResize();
            }
        });

        // 监听浏览器前进后退
        window.addEventListener('popstate', () => {
            if (isFullscreenActive) {
                console.log('🔄 路由变化，保持全屏状态');
            }
        });

        console.log('🚀 全屏布局控制已初始化');

    } catch (error) {
        console.error('❌ 初始化全屏布局控制时发生错误:', error);
    }
}

/**
 * 销毁全屏布局控制
 * 在应用卸载时调用，清理所有监听器和状态
 */
export function destroyFullscreenLayout() {
    try {
        // 强制退出全屏
        if (isFullscreenActive) {
            showMenus();
        }

        // 禁用键盘快捷键
        disableKeyboardShortcut();

        // 禁用窗口大小变化监听
        if (resizeListener) {
            window.removeEventListener('resize', resizeListener);
            resizeListener = null;
        }

        // 清理防抖定时器
        if (resizeTimeoutId) {
            clearTimeout(resizeTimeoutId);
            resizeTimeoutId = null;
        }

        // 移除全屏按钮
        removeFullscreenToggleButton();

        // 清理状态
        isFullscreenActive = false;
        originalStyles.clear();

        console.log('🗑️ 全屏布局控制已销毁');

    } catch (error) {
        console.error('❌ 销毁全屏布局控制时发生错误:', error);
    }
}
