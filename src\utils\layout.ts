/**
 * 全局布局控制工具函数
 * 用于动态控制页面布局元素的显示和隐藏
 * 支持所有布局模式：defaults、classic、transverse、columns
 */

import { useThemeConfig } from '/@/stores/themeConfig';
import { storeToRefs } from 'pinia';

// 全局状态管理
let isFullscreenActive = false;
let originalStyles: Map<string, string> = new Map();
let keyboardListener: ((e: KeyboardEvent) => void) | null = null;

/**
 * 保存元素的原始样式
 */
function saveOriginalStyles(element: HTMLElement, selector: string) {
    if (element && !originalStyles.has(selector)) {
        originalStyles.set(selector, element.style.cssText);
    }
}

/**
 * 恢复元素的原始样式
 */
function restoreOriginalStyles(element: HTMLElement, selector: string) {
    if (element && originalStyles.has(selector)) {
        element.style.cssText = originalStyles.get(selector) || '';
    }
}

/**
 * 隐藏所有菜单和布局元素
 */
export function hideMenus() {
    if (isFullscreenActive) return;

    // 获取主题配置
    const themeConfigStore = useThemeConfig();
    const { themeConfig } = storeToRefs(themeConfigStore);
    const currentLayout = themeConfig.value.layout;

    // 隐藏左侧菜单（所有布局都有）
    const asideElement = document.querySelector('.layout-aside') as HTMLElement;
    if (asideElement) {
        saveOriginalStyles(asideElement, '.layout-aside');
        asideElement.style.display = 'none';
    }

    // 隐藏分栏菜单（columns布局特有）
    const columnsAsideElement = document.querySelector('.layout-columns-aside') as HTMLElement;
    if (columnsAsideElement) {
        saveOriginalStyles(columnsAsideElement, '.layout-columns-aside');
        columnsAsideElement.style.display = 'none';
    }

    // 隐藏顶部菜单
    const headerElement = document.querySelector('.layout-header') as HTMLElement;
    if (headerElement) {
        saveOriginalStyles(headerElement, '.layout-header');
        headerElement.style.display = 'none';
    }

    // 隐藏面包屑导航
    const breadcrumbElement = document.querySelector('.layout-navbars-breadcrumb') as HTMLElement;
    if (breadcrumbElement) {
        saveOriginalStyles(breadcrumbElement, '.layout-navbars-breadcrumb');
        breadcrumbElement.style.display = 'none';
    }

    // 隐藏标签页
    const tagsViewElement = document.querySelector('.layout-navbars-tagsview') as HTMLElement;
    if (tagsViewElement) {
        saveOriginalStyles(tagsViewElement, '.layout-navbars-tagsview');
        tagsViewElement.style.display = 'none';
    }

    // 隐藏Logo
    const logoElement = document.querySelector('.layout-logo') as HTMLElement;
    if (logoElement) {
        saveOriginalStyles(logoElement, '.layout-logo');
        logoElement.style.display = 'none';
    }

    // 隐藏底部版权信息
    const footerElement = document.querySelector('.layout-footer') as HTMLElement;
    if (footerElement) {
        saveOriginalStyles(footerElement, '.layout-footer');
        footerElement.style.display = 'none';
    }

    // 调整主内容区域样式，使其占满全屏
    const mainElement = document.querySelector('.layout-main') as HTMLElement;
    if (mainElement) {
        saveOriginalStyles(mainElement, '.layout-main');
        mainElement.style.width = '100vw';
        mainElement.style.height = '100vh';
        mainElement.style.padding = '0';
        mainElement.style.margin = '0';
        mainElement.style.position = 'fixed';
        mainElement.style.top = '0';
        mainElement.style.left = '0';
        mainElement.style.zIndex = '1000';
    }

    // 调整容器样式
    const containerElement = document.querySelector('.layout-container-view') as HTMLElement;
    if (containerElement) {
        saveOriginalStyles(containerElement, '.layout-container-view');
        containerElement.style.width = '100vw';
        containerElement.style.height = '100vh';
    }

    // 调整主容器样式
    const layoutContainer = document.querySelector('.layout-container') as HTMLElement;
    if (layoutContainer) {
        saveOriginalStyles(layoutContainer, '.layout-container');
        layoutContainer.style.width = '100vw';
        layoutContainer.style.height = '100vh';
    }

    // 特殊处理不同布局模式
    if (currentLayout === 'columns') {
        const columnsWrap = document.querySelector('.layout-columns-warp') as HTMLElement;
        if (columnsWrap) {
            saveOriginalStyles(columnsWrap, '.layout-columns-warp');
            columnsWrap.style.width = '100vw';
            columnsWrap.style.height = '100vh';
        }
    }

    // 添加全屏样式类
    document.body.classList.add('layout-fullscreen-mode');
    document.documentElement.classList.add('layout-fullscreen-mode');

    // 设置状态
    isFullscreenActive = true;

    // 触发自定义事件
    window.dispatchEvent(new CustomEvent('layout-fullscreen-enter'));
}

/**
 * 显示所有菜单和布局元素
 */
export function showMenus() {
    if (!isFullscreenActive) return;

    // 恢复左侧菜单
    const asideElement = document.querySelector('.layout-aside') as HTMLElement;
    if (asideElement) {
        restoreOriginalStyles(asideElement, '.layout-aside');
    }

    // 恢复分栏菜单
    const columnsAsideElement = document.querySelector('.layout-columns-aside') as HTMLElement;
    if (columnsAsideElement) {
        restoreOriginalStyles(columnsAsideElement, '.layout-columns-aside');
    }

    // 恢复顶部菜单
    const headerElement = document.querySelector('.layout-header') as HTMLElement;
    if (headerElement) {
        restoreOriginalStyles(headerElement, '.layout-header');
    }

    // 恢复面包屑导航
    const breadcrumbElement = document.querySelector('.layout-navbars-breadcrumb') as HTMLElement;
    if (breadcrumbElement) {
        restoreOriginalStyles(breadcrumbElement, '.layout-navbars-breadcrumb');
    }

    // 恢复标签页
    const tagsViewElement = document.querySelector('.layout-navbars-tagsview') as HTMLElement;
    if (tagsViewElement) {
        restoreOriginalStyles(tagsViewElement, '.layout-navbars-tagsview');
    }

    // 恢复Logo
    const logoElement = document.querySelector('.layout-logo') as HTMLElement;
    if (logoElement) {
        restoreOriginalStyles(logoElement, '.layout-logo');
    }

    // 恢复底部版权信息
    const footerElement = document.querySelector('.layout-footer') as HTMLElement;
    if (footerElement) {
        restoreOriginalStyles(footerElement, '.layout-footer');
    }

    // 恢复主内容区域样式
    const mainElement = document.querySelector('.layout-main') as HTMLElement;
    if (mainElement) {
        restoreOriginalStyles(mainElement, '.layout-main');
    }

    // 恢复容器样式
    const containerElement = document.querySelector('.layout-container-view') as HTMLElement;
    if (containerElement) {
        restoreOriginalStyles(containerElement, '.layout-container-view');
    }

    // 恢复主容器样式
    const layoutContainer = document.querySelector('.layout-container') as HTMLElement;
    if (layoutContainer) {
        restoreOriginalStyles(layoutContainer, '.layout-container');
    }

    // 恢复分栏布局特殊样式
    const columnsWrap = document.querySelector('.layout-columns-warp') as HTMLElement;
    if (columnsWrap) {
        restoreOriginalStyles(columnsWrap, '.layout-columns-warp');
    }

    // 移除全屏样式类
    document.body.classList.remove('layout-fullscreen-mode');
    document.documentElement.classList.remove('layout-fullscreen-mode');

    // 清空原始样式缓存
    originalStyles.clear();

    // 设置状态
    isFullscreenActive = false;

    // 触发自定义事件
    window.dispatchEvent(new CustomEvent('layout-fullscreen-exit'));
}

/**
 * 切换菜单显示状态
 */
export function toggleMenus() {
    if (isFullscreenActive) {
        showMenus();
    } else {
        hideMenus();
    }
}

/**
 * 检查当前是否为全屏模式
 */
export function isFullscreenMode(): boolean {
    return isFullscreenActive;
}

/**
 * 强制退出全屏模式（用于页面刷新后的状态恢复）
 */
export function forceExitFullscreen() {
    if (document.body.classList.contains('layout-fullscreen-mode')) {
        document.body.classList.remove('layout-fullscreen-mode');
        document.documentElement.classList.remove('layout-fullscreen-mode');
    }
    isFullscreenActive = false;
    originalStyles.clear();
}

/**
 * 启用键盘快捷键支持
 * @param shortcutKey 快捷键组合，默认为 'F11'
 */
export function enableKeyboardShortcut(shortcutKey: string = 'F11') {
    // 移除之前的监听器
    if (keyboardListener) {
        document.removeEventListener('keydown', keyboardListener);
    }

    keyboardListener = (e: KeyboardEvent) => {
        // F11 键切换全屏
        if (e.key === shortcutKey) {
            e.preventDefault();
            toggleMenus();
        }
        // Ctrl+Shift+F 组合键切换全屏
        else if (e.ctrlKey && e.shiftKey && e.key === 'F') {
            e.preventDefault();
            toggleMenus();
        }
        // ESC 键退出全屏
        else if (e.key === 'Escape' && isFullscreenActive) {
            e.preventDefault();
            showMenus();
        }
    };

    document.addEventListener('keydown', keyboardListener);
}

/**
 * 禁用键盘快捷键支持
 */
export function disableKeyboardShortcut() {
    if (keyboardListener) {
        document.removeEventListener('keydown', keyboardListener);
        keyboardListener = null;
    }
}

/**
 * 为页面添加全屏切换按钮
 * @param options 按钮配置选项
 */
export function addFullscreenToggleButton(options: {
    container?: string;
    buttonText?: string;
    position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
    style?: Partial<CSSStyleDeclaration>;
} = {}) {
    const {
        container = 'body',
        buttonText = '切换全屏',
        position = 'top-right',
        style = {}
    } = options;

    const containerElement = document.querySelector(container);
    if (!containerElement) return;

    // 检查是否已存在按钮
    if (document.querySelector('.layout-fullscreen-toggle-btn')) return;

    const button = document.createElement('button');
    button.className = 'layout-fullscreen-toggle-btn';
    button.textContent = isFullscreenActive ? '退出全屏' : buttonText;

    // 设置按钮位置
    const positions = {
        'top-right': { top: '20px', right: '20px' },
        'top-left': { top: '20px', left: '20px' },
        'bottom-right': { bottom: '20px', right: '20px' },
        'bottom-left': { bottom: '20px', left: '20px' }
    };

    const positionStyle = positions[position];

    // 默认样式
    const defaultStyle = {
        position: 'fixed',
        zIndex: '9999',
        padding: '10px 20px',
        background: '#409eff',
        color: 'white',
        border: 'none',
        borderRadius: '6px',
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: '500',
        boxShadow: '0 4px 12px rgba(64, 158, 255, 0.3)',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        userSelect: 'none',
        ...positionStyle,
        ...style
    };

    // 应用样式
    Object.assign(button.style, defaultStyle);

    // 添加事件监听
    button.addEventListener('mouseenter', () => {
        button.style.background = '#66b1ff';
        button.style.transform = 'translateY(-2px)';
        button.style.boxShadow = '0 6px 16px rgba(64, 158, 255, 0.4)';
    });

    button.addEventListener('mouseleave', () => {
        button.style.background = '#409eff';
        button.style.transform = 'translateY(0)';
        button.style.boxShadow = '0 4px 12px rgba(64, 158, 255, 0.3)';
    });

    button.addEventListener('click', () => {
        toggleMenus();
        button.textContent = isFullscreenActive ? '退出全屏' : buttonText;
    });

    // 监听全屏状态变化
    const updateButtonText = () => {
        button.textContent = isFullscreenActive ? '退出全屏' : buttonText;
    };

    window.addEventListener('layout-fullscreen-enter', updateButtonText);
    window.addEventListener('layout-fullscreen-exit', updateButtonText);

    containerElement.appendChild(button);
}

/**
 * 移除全屏切换按钮
 */
export function removeFullscreenToggleButton() {
    const button = document.querySelector('.layout-fullscreen-toggle-btn');
    if (button) {
        button.remove();
    }
}

/**
 * 组合式API：使用全屏布局控制
 * @returns 全屏控制相关的方法和状态
 */
export function useFullscreenLayout() {
    // 动态导入Vue函数，避免在非Vue环境中出错
    let vueRef: any, vueComputed: any, vueOnMounted: any, vueOnUnmounted: any;

    try {
        const vue = require('vue');
        vueRef = vue.ref;
        vueComputed = vue.computed;
        vueOnMounted = vue.onMounted;
        vueOnUnmounted = vue.onUnmounted;
    } catch (error) {
        // 如果Vue不可用，返回基础实现
        console.warn('Vue not available, using basic implementation');
        return {
            isFullscreen: { value: isFullscreenActive },
            enterFullscreen: hideMenus,
            exitFullscreen: showMenus,
            toggleFullscreen: toggleMenus,
            addToggleButton: addFullscreenToggleButton,
            removeToggleButton: removeFullscreenToggleButton,
            enableKeyboard: enableKeyboardShortcut,
            disableKeyboard: disableKeyboardShortcut
        };
    }

    const isFullscreen = vueRef(isFullscreenActive);

    // 更新响应式状态
    const updateFullscreenState = () => {
        isFullscreen.value = isFullscreenActive;
    };

    // 进入全屏
    const enterFullscreen = () => {
        hideMenus();
        updateFullscreenState();
    };

    // 退出全屏
    const exitFullscreen = () => {
        showMenus();
        updateFullscreenState();
    };

    // 切换全屏
    const toggleFullscreen = () => {
        toggleMenus();
        updateFullscreenState();
    };

    // 监听全屏状态变化
    if (vueOnMounted) {
        vueOnMounted(() => {
            window.addEventListener('layout-fullscreen-enter', updateFullscreenState);
            window.addEventListener('layout-fullscreen-exit', updateFullscreenState);

            // 启用键盘快捷键
            enableKeyboardShortcut();

            // 页面刷新时强制退出全屏
            forceExitFullscreen();
        });
    }

    if (vueOnUnmounted) {
        vueOnUnmounted(() => {
            window.removeEventListener('layout-fullscreen-enter', updateFullscreenState);
            window.removeEventListener('layout-fullscreen-exit', updateFullscreenState);

            // 禁用键盘快捷键
            disableKeyboardShortcut();

            // 移除全屏按钮
            removeFullscreenToggleButton();
        });
    }

    return {
        isFullscreen: vueComputed ? vueComputed(() => isFullscreen.value) : isFullscreen,
        enterFullscreen,
        exitFullscreen,
        toggleFullscreen,
        addToggleButton: addFullscreenToggleButton,
        removeToggleButton: removeFullscreenToggleButton,
        enableKeyboard: enableKeyboardShortcut,
        disableKeyboard: disableKeyboardShortcut
    };
}

/**
 * 自动初始化全屏布局控制
 * 在应用启动时调用，设置全局快捷键和状态管理
 */
export function initFullscreenLayout() {
    // 页面加载时强制退出全屏状态
    forceExitFullscreen();

    // 启用全局键盘快捷键
    enableKeyboardShortcut();

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', () => {
        if (document.hidden && isFullscreenActive) {
            // 页面隐藏时记录状态，但不退出全屏
            console.log('页面隐藏，保持全屏状态');
        }
    });

    // 监听窗口大小变化
    window.addEventListener('resize', () => {
        if (isFullscreenActive) {
            // 窗口大小变化时重新调整布局
            setTimeout(() => {
                const mainElement = document.querySelector('.layout-main') as HTMLElement;
                if (mainElement) {
                    mainElement.style.width = '100vw';
                    mainElement.style.height = '100vh';
                }
            }, 100);
        }
    });

    console.log('全屏布局控制已初始化');
}

/**
 * 销毁全屏布局控制
 * 在应用卸载时调用，清理所有监听器和状态
 */
export function destroyFullscreenLayout() {
    // 强制退出全屏
    if (isFullscreenActive) {
        showMenus();
    }

    // 禁用键盘快捷键
    disableKeyboardShortcut();

    // 移除全屏按钮
    removeFullscreenToggleButton();

    // 清理状态
    isFullscreenActive = false;
    originalStyles.clear();

    console.log('全屏布局控制已销毁');
}
