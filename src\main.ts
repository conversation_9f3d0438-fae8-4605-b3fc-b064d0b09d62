import { createApp } from 'vue';
import pinia from '/@/stores/index';
import App from '/@/App.vue';
import router from '/@/router';
import { i18n } from '/@/i18n/index';
import { directive } from '/@/directive/index';
import VueGridLayout from 'vue-grid-layout';
import other from '/@/utils/other';
import globalComponent from '/@/components/index'
import ElementPlus from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';  // 导入所有图标
import '/@/theme/index.scss';
import dialogDrag from '/@/directive/dialogDrag';
import { configureECharts } from '/@/utils/echarts-config';
import { initFullscreenLayout } from '/@/utils/layout';
const app = createApp(App);

// 配置 ECharts 被动事件监听器
configureECharts();
app.directive('dialogDrag', dialogDrag);
directive(app);
other.elSvg(app);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component);  // 注册每个图标
  }
app.use(pinia)
app.use(router)
app.use(ElementPlus)
app.use(i18n)
app.use(VueGridLayout)

// 注册全局的组件
for (const componentItme in globalComponent) {
    app.component(componentItme, globalComponent[componentItme])
}
app.directive('resize', {
    mounted(el, binding) {
        const resizeObserver = new ResizeObserver(() => {
            binding.value()
        })
        resizeObserver.observe(el)
        el.__resizeObserver__ = resizeObserver
    },
    unmounted(el) {
        el.__resizeObserver__?.disconnect()
    }
})

import * as monaco from 'monaco-editor';
import editorWorker from 'monaco-editor/esm/vs/editor/editor.worker?worker';
import jsonWorker from 'monaco-editor/esm/vs/language/json/json.worker?worker';
import cssWorker from 'monaco-editor/esm/vs/language/css/css.worker?worker';
import htmlWorker from 'monaco-editor/esm/vs/language/html/html.worker?worker';
import tsWorker from 'monaco-editor/esm/vs/language/typescript/ts.worker?worker';

// 设置 Monaco Editor 环境配置
(window as any).MonacoEnvironment = {
    getWorker(_: any, label: string) {
        if (label === 'json') {
            return new jsonWorker();
        }
        if (label === 'css' || label === 'scss' || label === 'less') {
            return new cssWorker();
        }
        if (label === 'html' || label === 'handlebars' || label === 'razor') {
            return new htmlWorker();
        }
        if (label === 'typescript' || label === 'javascript') {
            return new tsWorker();
        }
        return new editorWorker();
    }
};

// 修复Monaco Editor的被动事件监听器问题
const originalAddEventListener = EventTarget.prototype.addEventListener;
EventTarget.prototype.addEventListener = function(type, listener, options) {
    if (type === 'wheel' || type === 'mousewheel') {
        // 对于滚轮事件，确保不是被动的
        if (typeof options === 'object') {
            options.passive = false;
        } else if (typeof options === 'boolean') {
            options = { capture: options, passive: false };
        } else {
            options = { passive: false };
        }
    }
    return originalAddEventListener.call(this, type, listener, options);
};

// 初始化全屏布局控制
initFullscreenLayout();

// 挂载应用（必须在所有配置完成后）
app.mount('#app');