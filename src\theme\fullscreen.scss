/* 全屏布局模式样式
------------------------------- */

/* 全屏模式基础样式 */
.layout-fullscreen-mode {
  /* 隐藏滚动条，防止页面滚动 */
  overflow: hidden !important;
  
  /* 确保全屏元素层级正确 */
  .layout-main {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 1000 !important;
    padding: 0 !important;
    margin: 0 !important;
    background: var(--el-bg-color, #ffffff);
    box-sizing: border-box !important;
    
    /* 确保内容区域正确显示 */
    .layout-main-scroll {
      width: 100% !important;
      height: 100% !important;
      
      .el-scrollbar__wrap {
        width: 100% !important;
        height: 100% !important;
        overflow-x: hidden !important;
        overflow-y: auto !important;
      }
      
      .el-scrollbar__view {
        width: 100% !important;
        min-height: 100% !important;
        box-sizing: border-box !important;
      }
    }
  }
  
  /* 隐藏所有布局装饰元素 */
  .layout-aside,
  .layout-header,
  .layout-navbars-breadcrumb,
  .layout-navbars-tagsview,
  .layout-logo,
  .layout-footer,
  .layout-columns-aside {
    display: none !important;
    visibility: hidden !important;
  }
  
  /* 调整容器样式 */
  .layout-container,
  .layout-container-view,
  .layout-columns-warp {
    width: 100vw !important;
    height: 100vh !important;
    box-sizing: border-box !important;
  }
  
  /* 滚动条容器调整 */
  .layout-backtop {
    width: 100vw !important;
    height: 100vh !important;
    
    .el-scrollbar {
      width: 100% !important;
      height: 100% !important;
    }
  }
  
  /* 确保内容区域可以正常滚动 */
  .layout-padding,
  .layout-padding-auto {
    width: 100% !important;
    height: 100% !important;
    position: relative !important;
    box-sizing: border-box !important;
    
    &-view {
      width: 100% !important;
      height: 100% !important;
      border: none !important;
      border-radius: 0 !important;
      box-sizing: border-box !important;
    }
  }
  
  /* 路由视图容器 */
  .layout-parent {
    width: 100% !important;
    height: 100% !important;
    box-sizing: border-box !important;
  }
}

/* 全屏切换按钮样式 */
.layout-fullscreen-toggle-btn {
  /* 基础样式在 JavaScript 中设置 */
  
  /* 响应式设计 */
  @media (max-width: 768px) {
    padding: 8px 16px !important;
    font-size: 12px !important;
    border-radius: 4px !important;
  }
  
  /* 深色模式适配 */
  [data-theme='dark'] & {
    background: var(--el-color-primary) !important;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2) !important;
    
    &:hover {
      background: var(--el-color-primary-light-3) !important;
      box-shadow: 0 6px 16px rgba(64, 158, 255, 0.3) !important;
    }
  }
}

/* 全屏模式下的特殊处理 */
.layout-fullscreen-mode {
  /* 确保弹窗等组件正常显示 */
  .el-dialog,
  .el-drawer,
  .el-message-box,
  .el-notification,
  .el-message,
  .el-loading-mask {
    z-index: 2000 !important;
  }
  
  /* 确保下拉菜单等组件正常显示 */
  .el-select-dropdown,
  .el-picker-panel,
  .el-cascader-panel,
  .el-color-picker__panel,
  .el-date-picker,
  .el-time-picker {
    z-index: 2100 !important;
  }
  
  /* 确保工具提示正常显示 */
  .el-tooltip__popper,
  .el-popover,
  .el-popconfirm {
    z-index: 2200 !important;
  }
  
  /* 确保回到顶部按钮正常显示 */
  .el-backtop {
    z-index: 1500 !important;
  }
  
  /* 确保加载动画正常显示 */
  .el-loading-mask {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
  }
}

/* 全屏模式动画效果 */
.layout-fullscreen-enter-active,
.layout-fullscreen-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.layout-fullscreen-enter-from {
  opacity: 0;
  transform: scale(0.95);
}

.layout-fullscreen-leave-to {
  opacity: 0;
  transform: scale(1.05);
}

/* 不同布局模式的特殊处理 */
.layout-fullscreen-mode {
  /* 默认布局 */
  &.layout-defaults {
    .layout-main {
      /* 默认布局特殊样式 */
    }
  }
  
  /* 经典布局 */
  &.layout-classic {
    .layout-main {
      /* 经典布局特殊样式 */
    }
  }
  
  /* 横向布局 */
  &.layout-transverse {
    .layout-main {
      /* 横向布局特殊样式 */
    }
  }
  
  /* 分栏布局 */
  &.layout-columns {
    .layout-main {
      /* 分栏布局特殊样式 */
    }
  }
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .layout-fullscreen-mode {
    .layout-main {
      /* 移动端全屏样式调整 */
      font-size: 14px;
    }
    
    .layout-fullscreen-toggle-btn {
      /* 移动端按钮样式调整 */
      bottom: 20px !important;
      right: 20px !important;
      top: auto !important;
      left: auto !important;
    }
  }
}

@media screen and (max-width: 480px) {
  .layout-fullscreen-mode {
    .layout-main {
      /* 小屏幕设备样式调整 */
      font-size: 12px;
    }
    
    .layout-fullscreen-toggle-btn {
      /* 小屏幕按钮样式调整 */
      padding: 6px 12px !important;
      font-size: 11px !important;
      bottom: 15px !important;
      right: 15px !important;
    }
  }
}

/* 打印模式下隐藏全屏按钮 */
@media print {
  .layout-fullscreen-toggle-btn {
    display: none !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .layout-fullscreen-toggle-btn {
    border: 2px solid currentColor !important;
    background: var(--el-color-primary) !important;
    color: var(--el-color-white) !important;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .layout-fullscreen-mode,
  .layout-fullscreen-toggle-btn,
  .layout-fullscreen-enter-active,
  .layout-fullscreen-leave-active {
    transition: none !important;
    animation: none !important;
  }
}

/* 深色模式下的全屏样式 */
[data-theme='dark'] {
  .layout-fullscreen-mode {
    .layout-main {
      background: var(--next-bg-main, #1f1f1f) !important;
      color: var(--next-color-white, #ffffff) !important;
    }
  }
}

/* 确保全屏模式下的内容可访问性 */
.layout-fullscreen-mode {
  /* 焦点管理 */
  .layout-main {
    &:focus-within {
      outline: none;
    }
  }
  
  /* 确保键盘导航正常工作 */
  *:focus {
    outline: 2px solid var(--el-color-primary);
    outline-offset: 2px;
  }
}
