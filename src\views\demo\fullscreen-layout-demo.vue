<template>
    <div class="fullscreen-layout-demo">
        <div class="demo-header">
            <h1>全屏布局控制演示</h1>
            <p>这个页面演示如何使用全新的全屏布局控制功能</p>
            <div class="status-badge">
                <el-tag :type="isFullscreen ? 'warning' : 'success'" size="large">
                    <el-icon><ele-Monitor /></el-icon>
                    {{ isFullscreen ? '全屏模式' : '正常模式' }}
                </el-tag>
            </div>
        </div>

        <div class="demo-content">
            <!-- 基础控制面板 -->
            <el-card class="control-panel" shadow="hover">
                <template #header>
                    <div class="card-header">
                        <span>基础控制</span>
                        <el-icon><ele-Setting /></el-icon>
                    </div>
                </template>
                
                <div class="button-grid">
                    <el-button type="primary" size="large" @click="enterFullscreen">
                        <el-icon><ele-FullScreen /></el-icon>
                        进入全屏
                    </el-button>
                    
                    <el-button type="success" size="large" @click="exitFullscreen">
                        <el-icon><ele-ScaleToOriginal /></el-icon>
                        退出全屏
                    </el-button>
                    
                    <el-button type="warning" size="large" @click="toggleFullscreen">
                        <el-icon><ele-Switch /></el-icon>
                        切换模式
                    </el-button>
                </div>
            </el-card>

            <!-- 高级功能面板 -->
            <el-card class="control-panel" shadow="hover">
                <template #header>
                    <div class="card-header">
                        <span>高级功能</span>
                        <el-icon><ele-Tools /></el-icon>
                    </div>
                </template>
                
                <div class="button-grid">
                    <el-button type="info" @click="addFloatingButton">
                        <el-icon><ele-Plus /></el-icon>
                        添加浮动按钮
                    </el-button>
                    
                    <el-button type="danger" @click="removeFloatingButton">
                        <el-icon><ele-Minus /></el-icon>
                        移除浮动按钮
                    </el-button>
                    
                    <el-button type="primary" @click="enableKeyboardShortcuts">
                        <el-icon><ele-Keyboard /></el-icon>
                        启用快捷键
                    </el-button>
                    
                    <el-button type="warning" @click="disableKeyboardShortcuts">
                        <el-icon><ele-Close /></el-icon>
                        禁用快捷键
                    </el-button>
                </div>
            </el-card>

            <!-- 功能说明 -->
            <el-card class="info-panel" shadow="hover">
                <template #header>
                    <div class="card-header">
                        <span>功能说明</span>
                        <el-icon><ele-InfoFilled /></el-icon>
                    </div>
                </template>
                
                <div class="info-content">
                    <h3>基础功能</h3>
                    <ul>
                        <li><strong>进入全屏：</strong>隐藏所有菜单和导航元素，内容区域占满全屏</li>
                        <li><strong>退出全屏：</strong>恢复所有菜单和导航元素的显示</li>
                        <li><strong>切换模式：</strong>在全屏模式和正常模式之间智能切换</li>
                    </ul>
                    
                    <h3>高级功能</h3>
                    <ul>
                        <li><strong>浮动按钮：</strong>在页面右下角添加/移除全屏切换按钮</li>
                        <li><strong>键盘快捷键：</strong>支持 F11、Ctrl+Shift+F、Alt+Enter 切换，ESC 退出全屏</li>
                        <li><strong>状态管理：</strong>自动保存和恢复布局状态</li>
                        <li><strong>响应式设计：</strong>适配不同屏幕尺寸</li>
                    </ul>
                    
                    <h3>支持的布局模式</h3>
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-tag type="info">默认布局</el-tag>
                        </el-col>
                        <el-col :span="6">
                            <el-tag type="success">经典布局</el-tag>
                        </el-col>
                        <el-col :span="6">
                            <el-tag type="warning">横向布局</el-tag>
                        </el-col>
                        <el-col :span="6">
                            <el-tag type="danger">分栏布局</el-tag>
                        </el-col>
                    </el-row>
                </div>
            </el-card>

            <!-- 键盘快捷键说明 -->
            <el-card class="shortcut-panel" shadow="hover">
                <template #header>
                    <div class="card-header">
                        <span>键盘快捷键</span>
                        <el-icon><ele-Keyboard /></el-icon>
                    </div>
                </template>
                
                <div class="shortcut-list">
                    <div class="shortcut-item">
                        <el-tag class="shortcut-key">F11</el-tag>
                        <span class="shortcut-desc">切换全屏模式</span>
                    </div>
                    <div class="shortcut-item">
                        <el-tag class="shortcut-key">Ctrl + Shift + F</el-tag>
                        <span class="shortcut-desc">切换全屏模式</span>
                    </div>
                    <div class="shortcut-item">
                        <el-tag class="shortcut-key">Alt + Enter</el-tag>
                        <span class="shortcut-desc">切换全屏模式</span>
                    </div>
                    <div class="shortcut-item">
                        <el-tag class="shortcut-key" type="warning">ESC</el-tag>
                        <span class="shortcut-desc">退出全屏模式</span>
                    </div>
                </div>
            </el-card>
        </div>
    </div>
</template>

<script setup lang="ts" name="fullscreenLayoutDemo">
import { onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { 
    useFullscreenLayout,
    addFullscreenToggleButton,
    removeFullscreenToggleButton,
    enableKeyboardShortcut,
    disableKeyboardShortcut
} from '/@/utils/layout';

// 使用全屏布局控制
const {
    isFullscreen,
    enterFullscreen,
    exitFullscreen,
    toggleFullscreen
} = useFullscreenLayout();

// 添加浮动按钮
const addFloatingButton = () => {
    addFullscreenToggleButton({
        position: 'bottom-right',
        buttonText: '全屏切换'
    });
    ElMessage.success('已添加浮动切换按钮');
};

// 移除浮动按钮
const removeFloatingButton = () => {
    removeFullscreenToggleButton();
    ElMessage.success('已移除浮动切换按钮');
};

// 启用键盘快捷键
const enableKeyboardShortcuts = () => {
    enableKeyboardShortcut();
    ElMessage.success('已启用键盘快捷键 (F11, Ctrl+Shift+F, Alt+Enter, ESC)');
};

// 禁用键盘快捷键
const disableKeyboardShortcuts = () => {
    disableKeyboardShortcut();
    ElMessage.success('已禁用键盘快捷键');
};

onMounted(() => {
    // 页面加载时启用键盘快捷键
    enableKeyboardShortcut();
});
</script>

<style scoped lang="scss">
.fullscreen-layout-demo {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
    min-height: calc(100vh - 40px);
}

.demo-header {
    text-align: center;
    margin-bottom: 30px;

    h1 {
        color: #2c3e50;
        margin-bottom: 10px;
        font-size: 32px;
        font-weight: bold;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    p {
        color: #7f8c8d;
        font-size: 16px;
        margin-bottom: 20px;
    }

    .status-badge {
        display: inline-block;

        .el-tag {
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;

            .el-icon {
                margin-right: 6px;
            }
        }
    }
}

.demo-content {
    display: grid;
    gap: 24px;
    grid-template-columns: 1fr;
}

.control-panel,
.info-panel,
.shortcut-panel {
    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: bold;
        font-size: 16px;
        color: #2c3e50;

        .el-icon {
            color: #409eff;
        }
    }
}

.button-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;

    .el-button {
        height: 48px;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .el-icon {
            margin-right: 8px;
        }
    }
}

.info-content {
    h3 {
        color: #409eff;
        margin: 20px 0 12px 0;
        font-size: 18px;

        &:first-child {
            margin-top: 0;
        }
    }

    ul {
        margin-bottom: 20px;
        padding-left: 20px;

        li {
            margin-bottom: 8px;
            line-height: 1.6;
            color: #606266;

            strong {
                color: #409eff;
                font-weight: 600;
            }
        }
    }

    .el-row {
        margin-top: 16px;

        .el-tag {
            width: 100%;
            text-align: center;
            padding: 8px 0;
            font-weight: 500;
        }
    }
}

.shortcut-list {
    display: grid;
    gap: 12px;

    .shortcut-item {
        display: flex;
        align-items: center;
        gap: 16px;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
            background: #e9ecef;
            transform: translateX(4px);
        }

        .shortcut-key {
            min-width: 120px;
            text-align: center;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-weight: bold;
        }

        .shortcut-desc {
            color: #606266;
            font-size: 14px;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .fullscreen-layout-demo {
        padding: 15px;
    }

    .demo-header {
        h1 {
            font-size: 24px;
        }
    }

    .button-grid {
        grid-template-columns: 1fr;

        .el-button {
            width: 100%;
        }
    }

    .info-content {
        .el-row {
            .el-col {
                margin-bottom: 8px;
            }
        }
    }

    .shortcut-list {
        .shortcut-item {
            flex-direction: column;
            text-align: center;
            gap: 8px;

            .shortcut-key {
                min-width: auto;
            }
        }
    }
}

@media (max-width: 480px) {
    .demo-header {
        h1 {
            font-size: 20px;
        }

        p {
            font-size: 14px;
        }
    }

    .button-grid {
        .el-button {
            height: 40px;
            font-size: 13px;
        }
    }
}

// 深色模式适配
[data-theme='dark'] {
    .fullscreen-layout-demo {
        .demo-header {
            h1 {
                background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }

            p {
                color: #a3a6ad;
            }
        }

        .card-header {
            color: #e5eaf3 !important;
        }

        .info-content {
            h3 {
                color: #409eff;
            }

            li {
                color: #a3a6ad;
            }
        }

        .shortcut-list {
            .shortcut-item {
                background: #2d2d2d;

                &:hover {
                    background: #3d3d3d;
                }

                .shortcut-desc {
                    color: #a3a6ad;
                }
            }
        }
    }
}

// 全屏模式下的特殊样式
.layout-fullscreen-mode {
    .fullscreen-layout-demo {
        padding: 40px;
        max-width: none;
        width: 100vw;
        height: 100vh;
        overflow-y: auto;

        .demo-header {
            h1 {
                font-size: 36px;
                background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }
        }

        .demo-content {
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        }
    }
}
</style>
