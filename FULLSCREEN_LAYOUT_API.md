# 全屏布局控制 API 文档

## 🎯 概述

这是一个完整的Vue 3全屏布局控制解决方案，支持动态隐藏/显示所有布局装饰元素，提供现代化的全屏浏览体验。

## 📚 核心API

### 基础函数

#### `hideMenus()`
隐藏所有菜单和布局装饰元素，进入全屏模式。

```typescript
import { hideMenus } from '/@/utils/layout';

hideMenus();
```

**功能：**
- 隐藏左侧菜单、顶部菜单、面包屑、标签页等
- 主内容区域调整为100vw x 100vh
- 添加全屏模式CSS类
- 触发`layout-fullscreen-enter`事件

#### `showMenus()`
显示所有菜单和布局装饰元素，退出全屏模式。

```typescript
import { showMenus } from '/@/utils/layout';

showMenus();
```

**功能：**
- 恢复所有布局元素的显示
- 恢复原始样式
- 移除全屏模式CSS类
- 触发`layout-fullscreen-exit`事件

#### `toggleMenus()`
在全屏模式和正常模式之间切换。

```typescript
import { toggleMenus } from '/@/utils/layout';

toggleMenus();
```

#### `isFullscreenMode(): boolean`
检查当前是否为全屏模式。

```typescript
import { isFullscreenMode } from '/@/utils/layout';

if (isFullscreenMode()) {
    console.log('当前处于全屏模式');
}
```

### 组合式API

#### `useFullscreenLayout()`
返回全屏布局控制的响应式状态和方法。

```typescript
import { useFullscreenLayout } from '/@/utils/layout';

const {
    isFullscreen,        // 响应式全屏状态
    enterFullscreen,     // 进入全屏
    exitFullscreen,      // 退出全屏
    toggleFullscreen     // 切换全屏
} = useFullscreenLayout();
```

**返回值：**
```typescript
interface FullscreenLayoutComposable {
    isFullscreen: ComputedRef<boolean>;
    enterFullscreen: () => void;
    exitFullscreen: () => void;
    toggleFullscreen: () => void;
}
```

### 高级功能

#### `enableKeyboardShortcut(shortcutKey?: string)`
启用键盘快捷键支持。

```typescript
import { enableKeyboardShortcut } from '/@/utils/layout';

// 使用默认快捷键 F11
enableKeyboardShortcut();

// 自定义主快捷键
enableKeyboardShortcut('F12');
```

**内置快捷键：**
- `F11` (或自定义键): 切换全屏
- `Ctrl+Shift+F`: 切换全屏
- `Alt+Enter`: 切换全屏
- `ESC`: 退出全屏

#### `disableKeyboardShortcut()`
禁用键盘快捷键支持。

```typescript
import { disableKeyboardShortcut } from '/@/utils/layout';

disableKeyboardShortcut();
```

#### `addFullscreenToggleButton(options)`
添加全屏切换浮动按钮。

```typescript
import { addFullscreenToggleButton } from '/@/utils/layout';

addFullscreenToggleButton({
    position: 'bottom-right',
    buttonText: '全屏切换',
    style: {
        background: '#ff6b6b',
        borderRadius: '50%'
    }
});
```

**选项参数：**
```typescript
interface ButtonOptions {
    container?: string;           // 容器选择器，默认 'body'
    buttonText?: string;          // 按钮文本，默认 '切换全屏'
    position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
    style?: Partial<CSSStyleDeclaration>;  // 自定义样式
}
```

#### `removeFullscreenToggleButton()`
移除全屏切换浮动按钮。

```typescript
import { removeFullscreenToggleButton } from '/@/utils/layout';

removeFullscreenToggleButton();
```

### 生命周期管理

#### `initFullscreenLayout()`
初始化全屏布局控制系统。

```typescript
import { initFullscreenLayout } from '/@/utils/layout';

initFullscreenLayout();
```

**功能：**
- 启用全局键盘快捷键
- 设置窗口大小变化监听
- 设置页面可见性变化监听
- 强制退出全屏状态（页面刷新后）

#### `destroyFullscreenLayout()`
销毁全屏布局控制系统。

```typescript
import { destroyFullscreenLayout } from '/@/utils/layout';

destroyFullscreenLayout();
```

**功能：**
- 清理所有事件监听器
- 移除浮动按钮
- 重置状态
- 清理样式缓存

## 🎨 事件系统

### 自定义事件

#### `layout-fullscreen-enter`
进入全屏模式时触发。

```typescript
window.addEventListener('layout-fullscreen-enter', (event) => {
    console.log('进入全屏模式', event.detail);
});
```

**事件详情：**
```typescript
{
    layout: string;      // 当前布局模式
    timestamp: number;   // 时间戳
}
```

#### `layout-fullscreen-exit`
退出全屏模式时触发。

```typescript
window.addEventListener('layout-fullscreen-exit', (event) => {
    console.log('退出全屏模式', event.detail);
});
```

#### `layout-fullscreen-resize`
全屏模式下窗口大小变化时触发。

```typescript
window.addEventListener('layout-fullscreen-resize', (event) => {
    console.log('全屏布局重新调整', event.detail);
});
```

**事件详情：**
```typescript
{
    width: number;       // 窗口宽度
    height: number;      // 窗口高度
    timestamp: number;   // 时间戳
}
```

## 🎯 使用示例

### Vue组件中使用

```vue
<template>
    <div>
        <el-button @click="toggleFullscreen">
            {{ isFullscreen ? '退出全屏' : '进入全屏' }}
        </el-button>
    </div>
</template>

<script setup>
import { useFullscreenLayout } from '/@/utils/layout';

const { isFullscreen, toggleFullscreen } = useFullscreenLayout();
</script>
```

### 工具函数使用

```typescript
import { 
    hideMenus, 
    showMenus, 
    toggleMenus,
    enableKeyboardShortcut,
    addFullscreenToggleButton 
} from '/@/utils/layout';

// 进入全屏
hideMenus();

// 退出全屏
showMenus();

// 切换全屏
toggleMenus();

// 启用快捷键
enableKeyboardShortcut();

// 添加浮动按钮
addFullscreenToggleButton({
    position: 'bottom-right'
});
```

### 事件监听

```typescript
// 监听全屏状态变化
window.addEventListener('layout-fullscreen-enter', () => {
    console.log('已进入全屏模式');
});

window.addEventListener('layout-fullscreen-exit', () => {
    console.log('已退出全屏模式');
});
```

## 🔧 配置选项

### CSS类名

- `.layout-fullscreen-mode`: 全屏模式根类
- `.layout-fullscreen-toggle-btn`: 切换按钮样式类

### 环境变量

在开发环境下，全局暴露测试函数：

```typescript
// 浏览器控制台中可用
window.layoutTest.run();    // 运行完整测试
window.layoutTest.quick();  // 快速测试
```

## 🐛 故障排除

### 常见问题

1. **按钮不显示**
   ```typescript
   // 检查容器是否存在
   const container = document.querySelector('body');
   if (container) {
       addFullscreenToggleButton();
   }
   ```

2. **快捷键不工作**
   ```typescript
   // 确保已启用快捷键
   enableKeyboardShortcut();
   
   // 检查是否有冲突
   disableKeyboardShortcut();
   enableKeyboardShortcut('F12'); // 使用其他键
   ```

3. **样式异常**
   ```scss
   // 确保导入了全屏样式
   @import '/@/theme/fullscreen.scss';
   ```

4. **状态不同步**
   ```typescript
   // 使用组合式API确保响应式
   const { isFullscreen } = useFullscreenLayout();
   
   // 或监听事件
   window.addEventListener('layout-fullscreen-enter', updateState);
   ```

## 📊 兼容性

- ✅ Vue 3.x
- ✅ Element Plus
- ✅ 现代浏览器 (Chrome 80+, Firefox 75+, Safari 13+)
- ✅ 移动端浏览器
- ✅ 深色模式
- ✅ 高对比度模式
- ✅ 减少动画模式

## 🚀 性能优化

1. **CSS优先**: 使用CSS控制显示隐藏，避免DOM操作
2. **状态缓存**: 保存原始样式，快速恢复
3. **事件防抖**: 窗口大小变化事件防抖处理
4. **内存管理**: 自动清理事件监听器和缓存
